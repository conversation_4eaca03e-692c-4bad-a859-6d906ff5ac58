{"name": "blocksmvp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "motion": "^12.23.12", "next": "15.5.2", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}