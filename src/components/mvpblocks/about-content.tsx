'use client';

import { motion } from 'framer-motion';
import { 
  Rocket, 
  Users, 
  Target, 
  Award,
  Database,
  Globe,
  Smartphone,
  Zap
} from 'lucide-react';

const skills = [
  { name: 'Frontend', icon: Globe, description: 'React, Next.js, TypeScript, Tailwind CSS' },
  { name: 'Backend', icon: Database, description: 'Node.js, Express, MongoDB, PostgreSQL' },
  { name: 'Mobile', icon: Smartphone, description: 'React Native, Flutter' },
  { name: 'DevO<PERSON>', icon: Zap, description: 'AWS, Docker, CI/CD, Vercel' },
];

const values = [
  {
    icon: Target,
    title: 'Mission-Driven',
    description: 'Every project I work on has a purpose - to solve real problems and create meaningful impact.'
  },
  {
    icon: Users,
    title: 'Collaborative',
    description: 'I believe the best solutions come from diverse perspectives and open communication.'
  },
  {
    icon: Rocket,
    title: 'Innovation-Focused',
    description: 'Always exploring new technologies and methodologies to stay ahead of the curve.'
  },
  {
    icon: Award,
    title: 'Quality-Obsessed',
    description: 'Committed to delivering exceptional work that exceeds expectations every time.'
  },
];

export default function AboutContent() {
  return (
    <div className="bg-background text-foreground">
      {/* Skills Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-light mb-4">
              Technical <span className="text-rose-500">Expertise</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Proficient across the full stack with a focus on modern, scalable technologies
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {skills.map((skill, index) => (
              <motion.div
                key={skill.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group p-6 rounded-xl border border-border bg-card hover:shadow-lg transition-all duration-300"
              >
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-rose-500 to-rose-700 mb-4 group-hover:scale-110 transition-transform duration-300">
                  <skill.icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">{skill.name}</h3>
                <p className="text-muted-foreground text-sm">{skill.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* About Story Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/30">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl md:text-4xl font-light mb-6">
                My <span className="text-rose-500">Journey</span>
              </h2>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  My passion for technology began early, driven by curiosity about how digital 
                  experiences shape our daily lives. What started as tinkering with code has 
                  evolved into a career dedicated to crafting exceptional web applications.
                </p>
                <p>
                  I specialize in the MERN stack and Next.js, with a particular focus on 
                  creating scalable, performant applications that prioritize user experience. 
                  Every project is an opportunity to learn something new and push the 
                  boundaries of what's possible.
                </p>
                <p>
                  When I'm not coding, you'll find me exploring new technologies, contributing 
                  to open source projects, or sharing knowledge with the developer community. 
                  I believe in the power of collaboration and continuous learning.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="aspect-square rounded-2xl bg-gradient-to-br from-rose-500/20 to-[#9b87f5]/20 p-8 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-4xl md:text-6xl font-light text-rose-500 mb-4">3+</div>
                  <div className="text-lg font-medium mb-2">Years Experience</div>
                  <div className="text-sm text-muted-foreground">Building digital solutions</div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-light mb-4">
              Core <span className="text-rose-500">Values</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              The principles that guide my work and drive my passion for development
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="p-8 rounded-xl border border-border bg-card hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-start gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-rose-500 to-rose-700 flex-shrink-0">
                    <value.icon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-3">{value.title}</h3>
                    <p className="text-muted-foreground">{value.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
