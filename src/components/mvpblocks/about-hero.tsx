'use client';

import { motion } from 'framer-motion';
import { Code, Heart, Lightbulb } from 'lucide-react';

export default function AboutHero() {
  return (
    <section
      className="relative w-full overflow-hidden bg-[#0a0613] pt-32 pb-10 font-light text-white antialiased md:pt-20 md:pb-16 h-screen flex items-center"
      style={{
        background: 'linear-gradient(135deg, #0a0613 0%, #150d27 100%)',
      }}
    >
      <div
        className="absolute top-0 right-0 h-1/2 w-1/2"
        style={{
          background:
            'radial-gradient(circle at 70% 30%, rgba(155, 135, 245, 0.15) 0%, rgba(13, 10, 25, 0) 60%)',
        }}
      />
      <div
        className="absolute top-0 left-0 h-1/2 w-1/2 -scale-x-100"
        style={{
          background:
            'radial-gradient(circle at 70% 30%, rgba(155, 135, 245, 0.15) 0%, rgba(13, 10, 25, 0) 60%)',
        }}
      />

      <div className="relative z-10 container mx-auto max-w-2xl px-4 text-center md:max-w-4xl md:px-6 lg:max-w-7xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <span className="mb-6 inline-block rounded-full border border-[#9b87f5]/30 px-3 py-1 text-xs text-[#9b87f5]">
            ABOUT ME • DEVELOPER & CREATOR
          </span>
          <h1 className="mx-auto mb-6 max-w-4xl text-4xl font-light md:text-5xl lg:text-7xl">
            Building the <span className="text-[#9b87f5]">future</span> <br />
            one line of code at a time
          </h1>
          <p className="mx-auto mb-10 max-w-2xl text-lg text-white/60 md:text-xl">
            I'm a passionate full-stack developer with a love for creating 
            innovative digital experiences. My journey in tech is driven by 
            curiosity, creativity, and a commitment to excellence.
          </p>

          <div className="mb-10 flex flex-col items-center justify-center gap-8 sm:mb-0 sm:flex-row">
            <motion.div 
              className="flex items-center gap-3 text-white/80"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-[#9b87f5]/20 to-[#9b87f5]/10 border border-[#9b87f5]/30">
                <Code className="h-6 w-6 text-[#9b87f5]" />
              </div>
              <span className="text-sm font-medium">Clean Code</span>
            </motion.div>
            
            <motion.div 
              className="flex items-center gap-3 text-white/80"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-rose-500/20 to-rose-700/10 border border-rose-500/30">
                <Heart className="h-6 w-6 text-rose-500" />
              </div>
              <span className="text-sm font-medium">User-Focused</span>
            </motion.div>
            
            <motion.div 
              className="flex items-center gap-3 text-white/80"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-yellow-500/20 to-orange-500/10 border border-yellow-500/30">
                <Lightbulb className="h-6 w-6 text-yellow-500" />
              </div>
              <span className="text-sm font-medium">Innovation</span>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
